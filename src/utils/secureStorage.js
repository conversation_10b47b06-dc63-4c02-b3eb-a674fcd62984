// Constants for storage keys
const STORAGE_KEYS = {
  SESSION_API_KEY: 'sessionApiKey',
  FALLBACK_OBFUSCATED_KEY: 'obfuscatedApiKey'
};

// Fallback security key for browsers that don't support chrome.storage.session
const SECURITY_KEY = 'GeminiTranslator2024';

/**
 * Validates a Gemini API key format
 * @param {string} apiKey - The API key to validate
 * @returns {boolean} - Whether the API key is valid
 */
function validateApiKey(apiKey) {
  if (!apiKey) return false;

  // Gemini API keys typically start with 'AI' and are 39 characters long
  // This is a basic validation and should be updated based on Google's actual format
  const isValidFormat = /^[A-Za-z0-9_-]{39}$/.test(apiKey);
  return isValidFormat;
}

/**
 * Fallback obfuscation for browsers without chrome.storage.session
 */
function obfuscateText(text, key) {
  if (!text) return '';

  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }

  return btoa(result);
}

/**
 * Fallback deobfuscation for browsers without chrome.storage.session
 */
function deobfuscateText(obfuscated, key) {
  if (!obfuscated) return '';

  try {
    const decoded = atob(obfuscated);

    let result = '';
    for (let i = 0; i < decoded.length; i++) {
      const charCode = decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode);
    }

    return result;
  } catch (error) {
    console.error('Deobfuscation error:', error);
    return '';
  }
}

/**
 * Securely stores the API key permanently using obfuscated storage in chrome.storage.local
 * @param {string} apiKey - The API key to store
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
async function secureSetApiKey(apiKey) {
  try {
    // If no API key, clear all stored keys
    if (!apiKey) {
      // Clear from both session and local storage
      if (chrome.storage.session) {
        await chrome.storage.session.remove([STORAGE_KEYS.SESSION_API_KEY]);
      }
      await chrome.storage.local.remove([STORAGE_KEYS.FALLBACK_OBFUSCATED_KEY, 'apiKey']);
      return true;
    }

    // Validate API key format
    if (!validateApiKey(apiKey)) {
      console.warn('Invalid API key format');
      // Still save it since validation might be imperfect, but log a warning
    }

    // Always store in local storage for persistence
    const obfuscatedKey = obfuscateText(apiKey, SECURITY_KEY);
    await chrome.storage.local.set({ [STORAGE_KEYS.FALLBACK_OBFUSCATED_KEY]: obfuscatedKey });

    // Also store in session for faster access if available
    if (chrome.storage.session) {
      await chrome.storage.session.set({ [STORAGE_KEYS.SESSION_API_KEY]: apiKey });
    }

    // Remove any plaintext version
    await chrome.storage.local.remove(['apiKey']);

    return true;
  } catch (error) {
    console.error("Error saving API key:", error);
    return false;
  }
}

/**
 * Migrates API keys from older storage formats to the new secure format
 */
async function migrateApiKey() {
  try {
    const result = await chrome.storage.local.get([
      'encryptedApiKey',
      'apiKey',
      'keyMigrated',
      STORAGE_KEYS.FALLBACK_OBFUSCATED_KEY
    ]);

    // Skip if already migrated
    if (result.keyMigrated) {
      return;
    }

    let apiKey = null;

    // Check for plaintext key (least secure, highest priority to migrate)
    if (result.apiKey) {
      apiKey = result.apiKey;
    }
    // Check for old encrypted key format
    else if (result.encryptedApiKey) {
      // Handle old encryption format if needed
      console.log('Found legacy encrypted key format');
    }

    if (apiKey) {
      // Save using the new secure method
      await secureSetApiKey(apiKey);
      // Clean up old storage keys
      await chrome.storage.local.remove(['apiKey', 'encryptedApiKey']);
      await chrome.storage.local.set({ keyMigrated: true });
    }
  } catch (error) {
    console.error('Error during API key migration:', error);
  }
}

/**
 * Securely retrieves the API key from storage
 * @returns {Promise<string|null>} - The API key or null if not found
 */
async function secureGetApiKey() {
  try {
    // First migrate any old keys to the new format
    await migrateApiKey();

    // Try to get from session storage first (faster)
    if (chrome.storage.session) {
      const sessionResult = await chrome.storage.session.get([STORAGE_KEYS.SESSION_API_KEY]);
      if (sessionResult[STORAGE_KEYS.SESSION_API_KEY]) {
        return sessionResult[STORAGE_KEYS.SESSION_API_KEY];
      }
    }

    // Get from local storage (permanent storage)
    const result = await chrome.storage.local.get([STORAGE_KEYS.FALLBACK_OBFUSCATED_KEY, 'apiKey']);

    // Try obfuscated key first
    if (result[STORAGE_KEYS.FALLBACK_OBFUSCATED_KEY]) {
      const apiKey = deobfuscateText(result[STORAGE_KEYS.FALLBACK_OBFUSCATED_KEY], SECURITY_KEY);
      if (apiKey) {
        // If session storage is available, store it there too for faster access
        if (chrome.storage.session) {
          await chrome.storage.session.set({ [STORAGE_KEYS.SESSION_API_KEY]: apiKey });
        }
        return apiKey;
      }
    }

    // Last resort: check for plaintext key and migrate it
    if (result.apiKey) {
      await secureSetApiKey(result.apiKey);
      await chrome.storage.local.remove('apiKey');
      return result.apiKey;
    }

    return null;
  } catch (error) {
    console.error("Error retrieving API key:", error);
    return null;
  }
}

export { secureSetApiKey, secureGetApiKey };
